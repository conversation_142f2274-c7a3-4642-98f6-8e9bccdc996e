package project

import (
	"net/http"

	"gitlab.finema.co/finema/csp/csp-api/requests"
	"gitlab.finema.co/finema/csp/csp-api/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type ProjectController struct {
}

func (m ProjectController) Pagination(c core.IHTTPContext) error {
	projectSvc := services.NewProjectService(c)
	
	// Check for filtering parameters
	ministryID := c.QueryParam("ministry_id")
	departmentID := c.QueryParam("department_id")
	divisionID := c.Query<PERSON>aram("division_id")
	createdByID := c.QueryParam("created_by_id")

	// Filter by ministry
	if ministryID != "" {
		res, ierr := projectSvc.FindByMinistry(ministryID, c.GetPageOptions())
		if ierr != nil {
			return c.JSON(ierr.GetStatus(), ierr.JSON())
		}
		return c.<PERSON>(http.StatusOK, res)
	}

	// Filter by department
	if departmentID != "" {
		res, ierr := projectSvc.FindByDepartment(departmentID, c.GetPageOptions())
		if ierr != nil {
			return c.JSON(ierr.GetStatus(), ierr.JSON())
		}
		return c.JSON(http.StatusOK, res)
	}

	// Filter by division
	if divisionID != "" {
		res, ierr := projectSvc.FindByDivision(divisionID, c.GetPageOptions())
		if ierr != nil {
			return c.JSON(ierr.GetStatus(), ierr.JSON())
		}
		return c.JSON(http.StatusOK, res)
	}

	// Filter by created by
	if createdByID != "" {
		res, ierr := projectSvc.FindByCreatedBy(createdByID, c.GetPageOptions())
		if ierr != nil {
			return c.JSON(ierr.GetStatus(), ierr.JSON())
		}
		return c.JSON(http.StatusOK, res)
	}

	// Default pagination without filters
	res, ierr := projectSvc.Pagination(c.GetPageOptions())
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m ProjectController) Find(c core.IHTTPContext) error {
	projectSvc := services.NewProjectService(c)
	project, err := projectSvc.Find(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, project)
}

func (m ProjectController) Create(c core.IHTTPContext) error {
	input := &requests.ProjectCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	projectSvc := services.NewProjectService(c)
	payload := &services.ProjectCreatePayload{}
	_ = utils.Copy(payload, input)
	project, err := projectSvc.Create(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, project)
}

func (m ProjectController) Update(c core.IHTTPContext) error {
	input := &requests.ProjectUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	projectSvc := services.NewProjectService(c)
	payload := &services.ProjectUpdatePayload{}
	_ = utils.Copy(payload, input)
	project, err := projectSvc.Update(c.Param("id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, project)
}

func (m ProjectController) Delete(c core.IHTTPContext) error {
	projectSvc := services.NewProjectService(c)
	err := projectSvc.Delete(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}
