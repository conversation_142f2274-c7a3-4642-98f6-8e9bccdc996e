package requests

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type ProjectCreate struct {
	core.BaseValidator
	Name         *string  `json:"name"`
	ContactName  *string  `json:"contact_name"`
	ContactPhone *string  `json:"contact_phone"`
	ContactEmail *string  `json:"contact_email"`
	Budget       *float64 `json:"budget"`
	MinistryID   *string  `json:"ministry_id"`
	DepartmentID *string  `json:"department_id"`
	DivisionID   *string  `json:"division_id"`
}

func (r *ProjectCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.<PERSON>tr<PERSON>equired(r.Name, "name"))

	// Validate email format if provided
	if r.ContactEmail != nil && *r.ContactEmail != "" {
		r.Must(r.IsEmail(r.ContactEmail, "contact_email"))
	}

	// Check if ministry exists if provided
	if r.MinistryID != nil && *r.MinistryID != "" {
		r.Must(r.IsExists(ctx, r.MinistryID, models.Ministry{}.TableName(), "id", "ministry_id"))
	}

	// Check if department exists if provided
	if r.DepartmentID != nil && *r.DepartmentID != "" {
		r.Must(r.IsExists(ctx, r.DepartmentID, models.Department{}.TableName(), "id", "department_id"))
	}

	// Check if division exists if provided
	if r.DivisionID != nil && *r.DivisionID != "" {
		r.Must(r.IsExists(ctx, r.DivisionID, models.Division{}.TableName(), "id", "division_id"))
	}

	return r.Error()
}

type ProjectUpdate struct {
	core.BaseValidator
	Name         *string  `json:"name"`
	ContactName  *string  `json:"contact_name"`
	ContactPhone *string  `json:"contact_phone"`
	ContactEmail *string  `json:"contact_email"`
	Budget       *float64 `json:"budget"`
	MinistryID   *string  `json:"ministry_id"`
	DepartmentID *string  `json:"department_id"`
	DivisionID   *string  `json:"division_id"`
}

func (r *ProjectUpdate) Valid(ctx core.IContext) core.IError {
	// Validate email format if provided
	if r.ContactEmail != nil && *r.ContactEmail != "" {
		r.Must(r.IsEmail(r.ContactEmail, "contact_email"))
	}

	// Check if ministry exists if provided
	if r.MinistryID != nil && *r.MinistryID != "" {
		r.Must(r.IsExists(ctx, r.MinistryID, models.Ministry{}.TableName(), "id", "ministry_id"))
	}

	// Check if department exists if provided
	if r.DepartmentID != nil && *r.DepartmentID != "" {
		r.Must(r.IsExists(ctx, r.DepartmentID, models.Department{}.TableName(), "id", "department_id"))
	}

	// Check if division exists if provided
	if r.DivisionID != nil && *r.DivisionID != "" {
		r.Must(r.IsExists(ctx, r.DivisionID, models.Division{}.TableName(), "id", "division_id"))
	}

	return r.Error()
}
