package services

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	"gitlab.finema.co/finema/csp/csp-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type IProjectService interface {
	Create(input *ProjectCreatePayload) (*models.Project, core.IError)
	Update(id string, input *ProjectUpdatePayload) (*models.Project, core.IError)
	Find(id string) (*models.Project, core.IError)
	Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.Project], core.IError)
	Delete(id string) core.IError
	FindByMinistry(ministryID string, pageOptions *core.PageOptions) (*repository.Pagination[models.Project], core.IError)
	FindByDepartment(departmentID string, pageOptions *core.PageOptions) (*repository.Pagination[models.Project], core.IError)
	FindByDivision(divisionID string, pageOptions *core.PageOptions) (*repository.Pagination[models.Project], core.IError)
	FindByCreatedBy(userID string, pageOptions *core.PageOptions) (*repository.Pagination[models.Project], core.IError)
}

type ProjectCreatePayload struct {
	Name         string   `json:"name"`
	ContactName  *string  `json:"contact_name"`
	ContactPhone *string  `json:"contact_phone"`
	ContactEmail *string  `json:"contact_email"`
	Budget       *float64 `json:"budget"`
	MinistryID   *string  `json:"ministry_id"`
	DepartmentID *string  `json:"department_id"`
	DivisionID   *string  `json:"division_id"`
}

type ProjectUpdatePayload struct {
	Name         *string  `json:"name"`
	ContactName  *string  `json:"contact_name"`
	ContactPhone *string  `json:"contact_phone"`
	ContactEmail *string  `json:"contact_email"`
	Budget       *float64 `json:"budget"`
	MinistryID   *string  `json:"ministry_id"`
	DepartmentID *string  `json:"department_id"`
	DivisionID   *string  `json:"division_id"`
}

type projectService struct {
	ctx core.IContext
}

func (s projectService) Create(input *ProjectCreatePayload) (*models.Project, core.IError) {
	user := s.ctx.GetUser()
	
	project := &models.Project{
		BaseModel:    models.NewBaseModel(),
		Name:         input.Name,
		ContactName:  input.ContactName,
		ContactPhone: input.ContactPhone,
		ContactEmail: input.ContactEmail,
		Budget:       input.Budget,
		MinistryID:   input.MinistryID,
		DepartmentID: input.DepartmentID,
		DivisionID:   input.DivisionID,
		CreatedByID:  utils.ToPointer(user.ID),
	}

	ierr := repo.Project(s.ctx).Create(project)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(project.ID)
}

func (s projectService) Update(id string, input *ProjectUpdatePayload) (*models.Project, core.IError) {
	project, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	user := s.ctx.GetUser()

	// Update fields if provided
	if input.Name != nil {
		project.Name = *input.Name
	}
	if input.ContactName != nil {
		project.ContactName = input.ContactName
	}
	if input.ContactPhone != nil {
		project.ContactPhone = input.ContactPhone
	}
	if input.ContactEmail != nil {
		project.ContactEmail = input.ContactEmail
	}
	if input.Budget != nil {
		project.Budget = input.Budget
	}
	if input.MinistryID != nil {
		project.MinistryID = input.MinistryID
	}
	if input.DepartmentID != nil {
		project.DepartmentID = input.DepartmentID
	}
	if input.DivisionID != nil {
		project.DivisionID = input.DivisionID
	}

	// Update metadata
	project.UpdatedAt = utils.GetCurrentDateTime()
	project.UpdatedByID = utils.ToPointer(user.ID)

	ierr = repo.Project(s.ctx).Where("id = ?", id).Updates(project)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(project.ID)
}

func (s projectService) Find(id string) (*models.Project, core.IError) {
	return repo.Project(s.ctx, repo.ProjectWithAllRelations()).FindOne("id = ?", id)
}

func (s projectService) Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.Project], core.IError) {
	return repo.Project(s.ctx, repo.ProjectOrderBy(pageOptions), repo.ProjectWithAllRelations()).Pagination(pageOptions)
}

func (s projectService) FindByMinistry(ministryID string, pageOptions *core.PageOptions) (*repository.Pagination[models.Project], core.IError) {
	return repo.Project(s.ctx, repo.ProjectByMinistry(ministryID), repo.ProjectOrderBy(pageOptions), repo.ProjectWithAllRelations()).Pagination(pageOptions)
}

func (s projectService) FindByDepartment(departmentID string, pageOptions *core.PageOptions) (*repository.Pagination[models.Project], core.IError) {
	return repo.Project(s.ctx, repo.ProjectByDepartment(departmentID), repo.ProjectOrderBy(pageOptions), repo.ProjectWithAllRelations()).Pagination(pageOptions)
}

func (s projectService) FindByDivision(divisionID string, pageOptions *core.PageOptions) (*repository.Pagination[models.Project], core.IError) {
	return repo.Project(s.ctx, repo.ProjectByDivision(divisionID), repo.ProjectOrderBy(pageOptions), repo.ProjectWithAllRelations()).Pagination(pageOptions)
}

func (s projectService) FindByCreatedBy(userID string, pageOptions *core.PageOptions) (*repository.Pagination[models.Project], core.IError) {
	return repo.Project(s.ctx, repo.ProjectByCreatedBy(userID), repo.ProjectOrderBy(pageOptions), repo.ProjectWithAllRelations()).Pagination(pageOptions)
}

func (s projectService) Delete(id string) core.IError {
	_, ierr := s.Find(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	user := s.ctx.GetUser()
	
	// Soft delete with deleted_by tracking
	updates := map[string]interface{}{
		"deleted_at":    utils.GetCurrentDateTime(),
		"deleted_by_id": user.ID,
	}

	return repo.Project(s.ctx).Where("id = ?", id).Updates(updates)
}

func NewProjectService(ctx core.IContext) IProjectService {
	return &projectService{ctx: ctx}
}
